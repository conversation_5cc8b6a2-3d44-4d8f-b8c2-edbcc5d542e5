# app/processing/queue_manager.py

import json
import logging
import time
from typing import Any, Optional

import redis

from app import settings


class TaskQueueManager:
    """Менеджер очередей Redis для атомарных операций с задачами.
    Реализует паттерн "захват-обработка-завершение" с отказоустойчивостью.

    📋 Полная документация очередей: doc/redis_queues.md
    """

    def __init__(self):
        self.redis_client = redis.from_url(settings.REDIS_URL)
        self.logger = logging.getLogger(__name__)

    def claim_task(self, timeout: int = 10) -> Optional[dict[str, Any]]:
        """Атомарно захватывает задачу из parsing_new и перемещает в parsing_processing.

        Args:
            timeout: Таймаут ожидания задачи в секундах

        Returns:
            Словарь с данными задачи или None если очередь пуста

        """
        try:
            # BRPOPLPUSH атомарно перемещает элемент между очередями
            #
            # АРХИТЕКТУРНОЕ РЕШЕНИЕ: Используем BRPOPLPUSH вместо BLMOVE
            #
            # ОБОСНОВАНИЕ:
            # - BRPOPLPUSH работает безупречно с Redis 2.2+ (максимальная совместимость)
            # - BLMOVE доступен только с Redis 6.2+ (ограничивает инфраструктуру)
            # - Оба обеспечивают одинаковую атомарность и производительность
            # - BRPOPLPUSH - проверенный временем стандарт для message queues
            # - Принцип "если работает идеально - не трогай"
            #
            # Текущая реализация обеспечивает 100% надежность и совместимость.
            raw_task = self.redis_client.brpoplpush(
                settings.QUEUE_PARSING_NEW,
                settings.QUEUE_PARSING_PROCESSING,
                timeout=timeout,
            )

            if raw_task is None:
                return None

            task_data = json.loads(raw_task.decode("utf-8"))
            task_data["_raw_task"] = raw_task.decode("utf-8")  # Сохраняем для удаления
            task_data["_claimed_at"] = time.time()

            self.logger.info(f"Захвачена задача: {task_data.get('file_path', 'unknown')}")
            return task_data

        except redis.RedisError as e:
            self.logger.error(f"Ошибка Redis при захвате задачи: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"Ошибка декодирования задачи: {e}")
            # Удаляем поврежденную задачу из очереди обработки
            self._remove_malformed_task(raw_task)
            return None

    def finalize_task(self, task_data: dict[str, Any], reason: str = "finalized") -> bool:
        """ГАРАНТИРОВАННО завершает жизненный цикл задачи.
        Удаляет из processing и SET_QUEUED_IDS, независимо от результата обработки.

        Используется для:
        - Успешно обработанных задач
        - Задач, отправленных в карантин
        - Задач с критическими ошибками

        Args:
            task_data: Данные задачи с ключом _raw_task
            reason: Причина завершения для логирования

        Returns:
            True если задача успешно финализирована
        """
        try:
            raw_task = task_data.get("_raw_task")
            if not raw_task:
                self.logger.warning("Нет _raw_task для финализации задачи")
                return False

            # Атомарно удаляем из processing И из SET активных задач
            pipe = self.redis_client.pipeline()
            pipe.lrem(settings.QUEUE_PARSING_PROCESSING, 1, raw_task)

            # Удаляем из SET активных задач
            source_type = task_data.get("source_type")
            source_id = task_data.get("source_id")
            if source_type and source_id:
                pipe.srem(settings.SET_QUEUED_IDS, f"{source_type}:{source_id}")

            results = pipe.execute()

            removed_count = results[0]
            if removed_count > 0:
                self.logger.info(f"Задача финализирована ({reason}): {task_data.get('file_path', 'unknown')}")
                return True
            else:
                self.logger.warning(f"Задача не найдена в очереди обработки: {task_data.get('file_path', 'unknown')}")
                return False

        except redis.RedisError as e:
            self.logger.error(f"Ошибка Redis при финализации задачи: {e}")
            return False

    def complete_task(self, task_data: dict[str, Any]) -> bool:
        """Завершает задачу парсинга - перемещает из parsing_processing в completed.
        Также удаляет из SET активных задач и АТОМАРНО обновляет кэш обработанных файлов.

        ИСПРАВЛЕНА ГОНКА СОСТОЯНИЙ: add_to_processed_cache интегрирован в pipeline
        для предотвращения дубликатов при падении воркера между операциями.

        Args:
            task_data: Данные задачи с ключом _raw_task

        Returns:
            True если задача успешно перемещена в completed

        """
        try:
            raw_task = task_data.get("_raw_task")
            if not raw_task:
                self.logger.warning("Нет _raw_task для завершения задачи")
                return False

            # Добавляем метку времени завершения
            completed_task = task_data.copy()
            completed_task["_completed_at"] = time.time()
            completed_task_json = json.dumps(
                {k: v for k, v in completed_task.items() if not k.startswith("_") or k == "_completed_at"}
            )

            # АТОМАРНАЯ ОПЕРАЦИЯ: перемещение + очистка SET + обновление кэша
            pipe = self.redis_client.pipeline()

            # Перемещение задачи: processing → completed
            pipe.lrem(settings.QUEUE_PARSING_PROCESSING, 1, raw_task)
            pipe.lpush(settings.QUEUE_COMPLETED, completed_task_json)
            # Автоматически очищаем completed очередь до максимального размера для предотвращения
            # бесконечного роста и переполнения памяти Redis. LTRIM сохраняет только последние N записей.
            pipe.ltrim(settings.QUEUE_COMPLETED, 0, settings.COMPLETED_QUEUE_MAX_SIZE - 1)

            # Удаляем из SET активных задач
            source_type = task_data.get("source_type")
            source_id = task_data.get("source_id")
            if source_type and source_id:
                pipe.srem(settings.SET_QUEUED_IDS, f"{source_type}:{source_id}")

                # КРИТИЧНОЕ ИСПРАВЛЕНИЕ: атомарно обновляем кэш обработанных файлов
                # Предотвращает повторную обработку при использовании --cache режима сканера
                cache_key = f"processed:{source_type}:{source_id}"
                pipe.sadd(settings.SET_PROCESSED, cache_key)

            results = pipe.execute()

            removed_count = results[0]
            if removed_count > 0:
                self.logger.info(f"Задача завершена атомарно: {task_data.get('file_path', 'unknown')}")
                if source_type and source_id:
                    self.logger.debug(f"🎯 Кэш обновлен атомарно: {source_type}:{source_id}")
                return True
            else:
                # Если не удалось удалить из processing, удаляем из completed
                self.redis_client.lrem(settings.QUEUE_COMPLETED, 1, completed_task_json)
                self.logger.warning(f"Задача не найдена в очереди обработки: {task_data.get('file_path', 'unknown')}")
                return False

        except redis.RedisError as e:
            self.logger.error(f"Ошибка Redis при завершении задачи: {e}")
            return False

    def return_task_to_queue(self, task_data: dict[str, Any]) -> bool:
        """Возвращает задачу обратно в parsing_new (для повторной обработки).
        SET активных задач остается неизменным.

        Args:
            task_data: Данные задачи

        Returns:
            True если задача успешно возвращена

        """
        try:
            # Убираем служебные поля
            clean_task = {k: v for k, v in task_data.items() if not k.startswith("_")}

            pipe = self.redis_client.pipeline()
            # Удаляем из processing и добавляем в new
            # SET активных задач НЕ трогаем - задача все еще в системе
            pipe.lrem(settings.QUEUE_PARSING_PROCESSING, 1, task_data.get("_raw_task", ""))
            pipe.lpush(settings.QUEUE_PARSING_NEW, json.dumps(clean_task))
            pipe.execute()

            self.logger.info(f"Задача возвращена в очередь: {task_data.get('file_path', 'unknown')}")
            return True

        except redis.RedisError as e:
            self.logger.error(f"Ошибка Redis при возврате задачи: {e}")
            return False

    def get_queue_stats(self) -> dict[str, int]:
        """Возвращает статистику очередей"""
        try:
            pipe = self.redis_client.pipeline()
            pipe.llen(settings.QUEUE_PARSING_NEW)
            pipe.llen(settings.QUEUE_PARSING_PROCESSING)
            pipe.llen(settings.QUEUE_COMPLETED)
            pipe.scard(settings.SET_PROCESSED)
            results = pipe.execute()

            return {
                "new_tasks": results[0],
                "processing_tasks": results[1],
                "completed_tasks": results[2],
                "cached_processed": results[3],
            }
        except redis.RedisError as e:
            self.logger.error(f"Ошибка получения статистики очередей: {e}")
            return {
                "new_tasks": -1,
                "processing_tasks": -1,
                "completed_tasks": -1,
                "cached_processed": -1,
            }

    def find_stale_tasks(self) -> list:
        """Находит зависшие задачи в parsing_processing старше WORKER_TIMEOUT.
        Возвращает список задач для восстановления.
        """
        try:
            current_time = time.time()
            stale_tasks = []

            # Получаем все задачи из очереди обработки
            processing_tasks = self.redis_client.lrange(settings.QUEUE_PARSING_PROCESSING, 0, -1)

            for raw_task in processing_tasks:
                try:
                    task_data = json.loads(raw_task.decode("utf-8"))
                    claimed_at = task_data.get("_claimed_at", current_time)

                    if current_time - claimed_at > settings.WORKER_TIMEOUT:
                        task_data["_raw_task"] = raw_task.decode("utf-8")
                        stale_tasks.append(task_data)

                except json.JSONDecodeError:
                    # Поврежденная задача тоже считается зависшей
                    stale_tasks.append({"_raw_task": raw_task.decode("utf-8"), "_malformed": True})

            if stale_tasks:
                self.logger.warning(f"Найдено {len(stale_tasks)} зависших задач")

            return stale_tasks

        except redis.RedisError as e:
            self.logger.error(f"Ошибка поиска зависших задач: {e}")
            return []

    def _remove_malformed_task(self, raw_task: bytes):
        """Удаляет поврежденную задачу из очереди обработки"""
        try:
            self.redis_client.lrem(settings.QUEUE_PARSING_PROCESSING, 1, raw_task)
            self.logger.warning("Удалена поврежденная задача из очереди")
        except redis.RedisError as e:
            self.logger.error(f"Ошибка удаления поврежденной задачи: {e}")

    def enqueue_rag_task(self, book_id: str) -> bool:
        """Ставит задачу в очередь RAG для обработки книги.

        Args:
            book_id: Уникальный идентификатор книги

        Returns:
            True если задача успешно поставлена в очередь

        """
        try:
            task_data = {
                "book_id": book_id,
                "task_type": "rag_processing",
                "created_at": time.time(),
            }

            self.redis_client.lpush(settings.QUEUE_CHUNKING_NEW, json.dumps(task_data))
            self.logger.info(f"Задача RAG поставлена в очередь: {book_id}")
            return True

        except redis.RedisError as e:
            self.logger.error(f"Ошибка постановки задачи RAG в очередь: {e}")
            return False


# Утилитарные функции для работы с Redis (перенесены из scanner.py)

def get_redis_connection() -> redis.Redis:
    """Получение соединения с Redis"""
    return redis.from_url(settings.REDIS_URL)


def rebuild_queued_set(redis_client: redis.Redis):
    """Восстанавливает SET активных задач из ВСЕХ очередей (new/processing/completed).

    КРИТИЧНО: Включает completed очередь для предотвращения дубликатов в случае падения
    воркера после завершения обработки, но до записи в PostgreSQL.

    ОПТИМИЗАЦИЯ ПАМЯТИ: Использует batch-обработку для больших очередей (100k+ элементов)
    вместо загрузки всех данных в память Python процесса.

    Используется при запуске системы для синхронизации.

    📋 Документация очередей: doc/redis_queues.md
    """
    from typing import cast

    try:
        # Очищаем старый SET
        redis_client.delete(settings.SET_QUEUED_IDS)

        task_keys = set()
        batch_size = 1000  # Обрабатываем по 1000 задач для оптимизации памяти

        # Список очередей для обработки
        queues = [
            (settings.QUEUE_PARSING_NEW, "new"),
            (settings.QUEUE_PARSING_PROCESSING, "processing"),
            (settings.QUEUE_COMPLETED, "completed"),
        ]

        for queue_name, queue_type in queues:
            queue_length_raw = redis_client.llen(queue_name)
            queue_length = cast(int, queue_length_raw)  # Явное приведение типа для mypy

            if queue_length == 0:
                continue

            logging.info(f"🔄 Обработка очереди {queue_type}: {queue_length} элементов")

            # Обрабатываем очередь батчами для экономии памяти
            for start_idx in range(0, queue_length, batch_size):
                end_idx = min(start_idx + batch_size - 1, queue_length - 1)

                # Загружаем только текущий batch вместо всей очереди
                batch_tasks_raw = redis_client.lrange(queue_name, start_idx, end_idx)
                batch_tasks = cast(list, batch_tasks_raw)

                for task_json in batch_tasks:
                    try:
                        task_data = json.loads(task_json)
                        source_type = task_data.get("source_type")
                        source_id = task_data.get("source_id")

                        if source_type and source_id:
                            task_key = f"{source_type}:{source_id}"
                            task_keys.add(task_key)

                    except (json.JSONDecodeError, KeyError) as e:
                        logging.warning(f"⚠️ Некорректная задача в очереди {queue_type}: {e}")
                        continue

        # Добавляем все ключи в SET одной операцией для оптимизации
        if task_keys:
            redis_client.sadd(settings.SET_QUEUED_IDS, *task_keys)

        logging.info(f"✅ SET восстановлен: {len(task_keys)} уникальных задач")

    except Exception as e:
        logging.error(f"❌ Критическая ошибка восстановления SET: {e}")
        raise

# app/processing/task_monitor.py

import json
import logging
import time
from pathlib import Path
from typing import Any, cast

import redis

from app import settings

from .queue_manager import TaskQueueManager


class TaskMonitor:
    """Монитор зависших задач для восстановления процессов после сбоев.
    Находит задачи в processing старше WORKER_TIMEOUT и возвращает их в очередь.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.queue_manager = TaskQueueManager()

    def check_and_recover_stale_tasks(self) -> dict[str, int]:
        """Проверяет и восстанавливает зависшие задачи.

        Returns:
            Статистика восстановления: {'recovered': int, 'quarantined': int, 'errors': int}

        """
        self.logger.info("🔍 Начинаем проверку зависших задач...")

        stats = {"recovered": 0, "quarantined": 0, "errors": 0}

        try:
            # Находим зависшие задачи
            stale_tasks = self.queue_manager.find_stale_tasks()

            if not stale_tasks:
                self.logger.info("✅ Зависших задач не найдено")
                return stats

            self.logger.warning(f"⚠️ Найдено {len(stale_tasks)} зависших задач")

            for task in stale_tasks:
                try:
                    if task.get("_malformed"):
                        # Поврежденная задача - просто удаляем
                        self._handle_malformed_task(task)
                        stats["errors"] += 1
                    else:
                        # Пытаемся восстановить задачу
                        if self._recover_stale_task(task):
                            stats["recovered"] += 1
                        else:
                            stats["quarantined"] += 1

                except Exception as e:
                    self.logger.error(f"❌ Ошибка при восстановлении задачи: {e}")
                    stats["errors"] += 1

            self.logger.info(
                f"📊 Восстановление завершено: восстановлено={stats['recovered']}, "
                f"в карантин={stats['quarantined']}, ошибок={stats['errors']}"
            )

        except Exception as e:
            self.logger.error(f"❌ Критическая ошибка при проверке зависших задач: {e}")
            stats["errors"] += 1

        return stats

    def _recover_stale_task(self, task_data: dict[str, Any]) -> bool:
        """Восстанавливает одну зависшую задачу.

        Returns:
            True если задача успешно восстановлена, False если отправлена в карантин

        """
        file_path = task_data.get("file_path", "unknown")
        self.logger.info(f"🔧 Восстанавливаем зависшую задачу: {Path(file_path).name}")

        try:
            # Проверяем состояние файла
            original_path = Path(file_path)
            source_type = task_data.get("source_type")

            if not source_type:
                self.logger.warning(f"⚠️ Нет source_type для задачи {file_path}")
                self._cleanup_stale_task(task_data, "Отсутствует source_type")
                return False

            # Определяем директории обработки
            from app.utils import get_source_dir_by_type

            source_dir = get_source_dir_by_type(source_type)
            if not source_dir:
                self.logger.warning(f"⚠️ Неизвестный source_type: {source_type}")
                self._cleanup_stale_task(task_data, f"Неизвестный source_type: {source_type}")
                return False

            dirs = settings.get_processing_directories(source_dir)

            # В новой архитектуре файлы не перемещаются, работаем только с Redis блокировками
            redis_client = redis.from_url(settings.REDIS_URL)

            # Проверяем, есть ли блокировка для этой задачи
            source_type = task_data.get("source_type")
            source_id = task_data.get("source_id")

            if source_type and source_id:
                lock_key = f"lock:book:{source_type}:{source_id}"

                # Удаляем зависшую блокировку
                if redis_client.exists(lock_key):
                    redis_client.delete(lock_key)
                    self.logger.info(f"🔓 Удалена зависшая блокировка: {lock_key}")

                # Проверяем, существует ли исходный файл
                if original_path.exists():
                    # Возвращаем задачу в очередь
                    clean_task = {k: v for k, v in task_data.items() if not k.startswith("_")}

                    # Возвращаем в очередь для повторной обработки
                    pipe = redis_client.pipeline()
                    pipe.lrem(settings.QUEUE_PARSING_PROCESSING, 1, task_data.get("_raw_task", ""))
                    pipe.lpush(settings.QUEUE_PARSING_NEW, json.dumps(clean_task))
                    pipe.execute()

                    self.logger.info(f"✅ Зависшая задача восстановлена: {source_type}:{source_id}")
                    return True
                else:
                    # Файл не найден - возможно уже обработан или удален
                    self.logger.warning(f"❓ Исходный файл не найден: {original_path}")
                    self._cleanup_stale_task(task_data, "Исходный файл не найден")
                    return False
            else:
                # Некорректная задача без source_type/source_id
                self.logger.warning(f"⚠️ Некорректная задача без source_type/source_id")
                self._cleanup_stale_task(task_data, "Missing source_type or source_id")
                return False

        except Exception as e:
            self.logger.error(f"❌ Ошибка восстановления задачи {file_path}: {e}")
            self._cleanup_stale_task(task_data, f"Ошибка восстановления: {e}")
            return False

    def _handle_malformed_task(self, task_data: dict[str, Any]):
        """Обрабатывает поврежденную задачу - удаляет из очереди и SET"""
        self.logger.warning("🗑️ Удаляем поврежденную задачу из очереди")

        try:
            # Для поврежденных задач тоже используем финализацию, если возможно
            if task_data.get("source_type") and task_data.get("source_id"):
                success = self.queue_manager.finalize_task(task_data, "поврежденная задача")
                if success:
                    self.logger.info("✅ Поврежденная задача финализирована")
                    return

            # Fallback для задач без source_type/source_id
            redis_client = redis.from_url(settings.REDIS_URL)
            redis_client.lrem(settings.QUEUE_PARSING_PROCESSING, 1, task_data.get("_raw_task", ""))
            self.logger.info("✅ Поврежденная задача удалена из очереди")

        except Exception as e:
            self.logger.error(f"❌ Ошибка удаления поврежденной задачи: {e}")

    def _cleanup_stale_task(self, task_data: dict[str, Any], reason: str):
        """Очищает зависшую задачу из очереди обработки И из SET активных задач"""
        try:
            # Используем финализацию для гарантированной очистки
            success = self.queue_manager.finalize_task(task_data, f"зависшая задача: {reason}")
            if success:
                self.logger.info(f"🧹 Зависшая задача финализирована: {reason}")
            else:
                self.logger.warning(f"⚠️ Не удалось финализировать зависшую задачу: {reason}")
        except Exception as e:
            self.logger.error(f"❌ Ошибка финализации зависшей задачи: {e}")

    def get_processing_files_stats(self) -> dict[str, Any]:
        """Возвращает статистику файлов в директориях обработки.
        В новой архитектуре in_progress не используется.
        """
        stats: dict[str, Any] = {
            "processed": 0,
            "quarantine": 0,
            "details": [],
        }

        try:
            for source_dir in settings.SOURCE_DIRS:
                dirs = settings.get_processing_directories(source_dir)

                source_stats: dict[str, Any] = {
                    "source_name": source_dir.name,
                    "processed": 0,
                    "quarantine": 0,
                }

                # Подсчитываем файлы только в processed и quarantine
                for dir_type, dir_path in dirs.items():
                    if dir_type in ("source", "in_progress"):  # Пропускаем source и устаревший in_progress
                        continue

                    if dir_path.exists():
                        file_count = len(list(dir_path.rglob("*")))
                        source_stats[dir_type] = file_count
                        current_count = cast(int, stats[dir_type])
                        stats[dir_type] = current_count + file_count

                details_list = cast(list[dict[str, Any]], stats["details"])
                details_list.append(source_stats)

        except Exception as e:
            self.logger.error(f"❌ Ошибка получения статистики файлов: {e}")
            stats["error"] = str(e)

        return stats

    def run_continuous_monitoring(self, check_interval: int = 300):
        """Запускает непрерывный мониторинг зависших задач.

        Args:
            check_interval: Интервал проверки в секундах (по умолчанию 5 минут)

        """
        self.logger.info(f"🎯 Запуск непрерывного мониторинга с интервалом {check_interval} сек")

        try:
            while True:
                stats = self.check_and_recover_stale_tasks()

                if stats["recovered"] > 0 or stats["quarantined"] > 0 or stats["errors"] > 0:
                    # Выводим статистику файлов при наличии активности
                    file_stats = self.get_processing_files_stats()
                    self.logger.info(f"📁 Файлов в обработке: {file_stats}")

                time.sleep(check_interval)

        except KeyboardInterrupt:
            self.logger.info("⏹️ Мониторинг остановлен пользователем")
        except Exception as e:
            self.logger.error(f"❌ Критическая ошибка мониторинга: {e}")
            raise

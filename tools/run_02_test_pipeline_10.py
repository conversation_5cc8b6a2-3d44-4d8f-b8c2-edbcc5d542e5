#!/usr/bin/env python3
"""
Полный интеграционный тест сканера источников (run_10_scan_sources.py).

ТЕСТИРУЕТ ПОЛНУЮ ЛОГИКУ СКАНЕРА:
- Создает реалистичную структуру тестовых источников с *.zip файлами
- Эмулирует различные сценарии: новые файлы, дубликаты в БД, задачи в очередях
- Использует ВСЕ реальные функции: extract_source_id, проверки дубликатов, формирование задач
- Тестирует режимы: без кэша, с кэшем, clear-cache, sync-only

ИСПОЛЬЗУЕТ ВСЕ РЕАЛЬНЫЕ КОМПОНЕНТЫ СКАНЕРА:
- scan_and_register_new_files() со всей бизнес-логикой
- is_source_processed() для проверки PostgreSQL дубликатов
- extract_source_id() для извлечения ID из имени файла
- Вся логика очередей Redis и кэша SET_PROCESSED

МОКАЮТСЯ ТОЛЬКО ОПЕРАЦИИ ЗАПИСИ:
- Redis LPUSH (добавление задач в QUEUE_NEW)
- Redis SADD (SET_QUEUED_IDS, SET_PROCESSED)
- PostgreSQL проверки is_source_processed()
- Операции синхронизации кэша с БД

РЕЗУЛЬТАТ: максимально честная диагностика реального поведения сканера с контролируемым окружением.
"""

import argparse
import json
import logging
import sys
import tempfile
import time
from pathlib import Path
from typing import Any, Optional, Union
from unittest.mock import MagicMock, patch

# Добавление корневой директории проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import settings
# УСТАРЕЛО: Тест для старого scanner.py - нужно обновить для scanner_inventorizer.py
# from app.ingestion.scanner import (
#     clear_redis_cache,
#     scan_and_register_new_files,
#     sync_redis_with_db,
# )


def create_test_sources_structure(temp_dir: Path) -> dict[str, Any]:
    """Создает реалистичную структуру тестовых источников с различными сценариями."""

    # Создаем тестовые директории источников
    test_sources: dict[str, Any] = {}

    for source_name, source_type in settings.SOURCE_TYPE_MAP.items():
        source_dir: Path = temp_dir / source_name
        source_dir.mkdir(parents=True, exist_ok=True)

        # Создаем поддиректории как в реальной системе
        (source_dir / "in_progress").mkdir(exist_ok=True)
        (source_dir / "processed").mkdir(exist_ok=True)
        (source_dir / "quarantine").mkdir(exist_ok=True)

        test_sources[source_name] = {
            "source_dir": source_dir,
            "source_type": source_type,
            "files": [],
        }

    # Создаем тестовые *.zip файлы с различными сценариями
    test_scenarios = [
        # Новые файлы для обработки
        {"filename": "123456.zip", "category": "new", "source": "zip_flibusta"},
        {"filename": "789012.zip", "category": "new", "source": "zip_searchfloor"},
        {"filename": "345678.zip", "category": "new", "source": "zip_anna"},
        # Файлы уже обработанные в БД (дубликаты)
        {
            "filename": "111111.zip",
            "category": "processed_in_db",
            "source": "zip_flibusta",
        },
        {
            "filename": "222222.zip",
            "category": "processed_in_db",
            "source": "zip_searchfloor",
        },
        # Файлы уже в очередях Redis
        {"filename": "333333.zip", "category": "queued", "source": "zip_flibusta"},
        {"filename": "444444.zip", "category": "queued", "source": "zip_anna"},
        # Файлы без корректного ID (будут пропущены)
        {
            "filename": "invalid_name.zip",
            "category": "invalid_id",
            "source": "zip_flibusta",
        },
        {
            "filename": "abc_def.zip",
            "category": "invalid_id",
            "source": "zip_searchfloor",
        },
        # Дополнительные новые файлы для статистики
        {"filename": "555555.zip", "category": "new", "source": "zip_anna"},
        {"filename": "666666.zip", "category": "new", "source": "zip_flibusta"},
    ]

    # Создаем физические файлы
    for scenario in test_scenarios:
        source_name = scenario["source"]
        source_info = test_sources[source_name]
        source_dir_path: Path = source_info["source_dir"]
        file_path: Path = source_dir_path / scenario["filename"]

        # Создаем пустой ZIP файл для тестирования
        file_path.write_bytes(b"PK\x03\x04")  # Минимальная ZIP сигнатура

        file_list: list[dict[str, Any]] = source_info["files"]
        file_list.append(
            {
                "path": file_path,
                "filename": scenario["filename"],
                "category": scenario["category"],
                "source_type": source_info["source_type"],
            }
        )

    return test_sources


def create_mock_redis_client() -> MagicMock:
    """Создает мок Redis клиента с реалистичным поведением."""

    # Эмулируем внутреннее состояние Redis с правильной типизацией
    redis_state: dict[str, dict[str, Union[list[str], set[str]]]] = {
        "queues": {
            settings.QUEUE_PARSING_NEW: [],
            settings.QUEUE_PARSING_PROCESSING: [],
            settings.QUEUE_COMPLETED: [],
        },
        "sets": {settings.SET_QUEUED_IDS: set(), settings.SET_PROCESSED: set()},
    }

    mock_redis = MagicMock()

    # Мокаем основные операции Redis
    def mock_lpush(queue_name: str, task_json: str) -> int:
        queue_dict = redis_state["queues"]
        queue_list = queue_dict.setdefault(queue_name, [])
        assert isinstance(queue_list, list)
        queue_list.append(task_json)
        return len(queue_list)

    def mock_sadd(set_name: str, *members: str) -> int:
        sets_dict = redis_state["sets"]
        redis_set = sets_dict.setdefault(set_name, set())
        assert isinstance(redis_set, set)
        redis_set.update(members)
        return len(members)

    def mock_sismember(set_name: str, member: str) -> bool:
        sets_dict = redis_state["sets"]
        redis_set = sets_dict.get(set_name, set())
        assert isinstance(redis_set, set)
        return member in redis_set

    def mock_delete(key_name: str) -> int:
        sets_dict = redis_state["sets"]
        if key_name in sets_dict:
            redis_set = sets_dict[key_name]
            assert isinstance(redis_set, set)
            count = len(redis_set)
            sets_dict[key_name] = set()
            return count
        return 0

    def mock_pipeline() -> MagicMock:
        pipe_mock = MagicMock()
        pipe_mock.lpush = mock_lpush
        pipe_mock.sadd = mock_sadd
        pipe_mock.execute.return_value = [1, 1]  # Успешные операции
        return pipe_mock

    # Подключаем моки к методам
    mock_redis.lpush = mock_lpush
    mock_redis.sadd = mock_sadd
    mock_redis.sismember = mock_sismember
    mock_redis.delete = mock_delete
    mock_redis.pipeline = mock_pipeline

    # Дополнительно мокаем методы для sync операций
    mock_redis.set.return_value = True  # sync lock
    mock_redis.llen.return_value = 0  # пустые очереди
    mock_redis.lrange.return_value = []
    mock_redis.memory_usage.return_value = 1024

    # Храним состояние для диагностики
    mock_redis._test_state = redis_state

    return mock_redis


def create_mock_db_connection(test_sources: dict[str, Any]) -> MagicMock:
    """Создает мок для подключения к БД с предустановленными дубликатами."""

    # Определяем какие файлы считаются уже обработанными в БД
    processed_files: set[tuple[int, int]] = set()

    for source_info in test_sources.values():
        for file_info in source_info["files"]:
            if file_info["category"] == "processed_in_db":
                # Извлекаем source_id из имени файла
                from app.utils import extract_source_id

                source_id = extract_source_id(Path(file_info["filename"]))
                if source_id:
                    processed_files.add((file_info["source_type"], source_id))

    def mock_db_query(query: str, params: tuple[int, int]) -> Optional[list[dict[str, bool]]]:
        """Мок для выполнения SQL запросов проверки дубликатов."""
        # Проверяем запрос is_source_processed
        if "book_sources" in query and "source_type" in query:
            source_type, source_id = params
            is_found = (source_type, source_id) in processed_files
            return [{"exists": True}] if is_found else None

        # Для остальных запросов возвращаем пустой результат
        return None

    # Создаем мок подключения
    mock_conn = MagicMock()
    mock_cursor = MagicMock()

    # Настраиваем cursor
    def mock_execute(query: str, params: Optional[tuple[int, int]] = None) -> None:
        mock_cursor._last_result = mock_db_query(query, params) if params else None

    def mock_fetchone() -> Optional[list[dict[str, bool]]]:
        return mock_cursor._last_result

    mock_cursor.execute = mock_execute
    mock_cursor.fetchone = mock_fetchone
    mock_cursor.fetchmany.return_value = []

    # Настраиваем контекстные менеджеры
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    mock_conn.cursor.return_value.__exit__.return_value = None

    # Контекстный менеджер для подключения
    mock_conn.__enter__.return_value = mock_conn
    mock_conn.__exit__.return_value = None

    return mock_conn


def setup_mock_queued_tasks(mock_redis: MagicMock, test_sources: dict[str, Any]) -> None:
    """Настраивает мок Redis с предзаполненными задачами в очередях."""

    # Добавляем файлы категории "queued" в SET_QUEUED_IDS
    for source_info in test_sources.values():
        for file_info in source_info["files"]:
            if file_info["category"] == "queued":
                from app.utils import extract_source_id

                source_id = extract_source_id(Path(file_info["filename"]))
                if source_id:
                    task_key = f"{file_info['source_type']}:{source_id}"
                    redis_state = mock_redis._test_state
                    sets_dict = redis_state["sets"]
                    queued_set = sets_dict[settings.SET_QUEUED_IDS]
                    assert isinstance(queued_set, set)
                    queued_set.add(task_key)


def run_scanner_test(temp_dir: Path, use_cache: bool = False) -> dict[str, Any]:
    """Запускает тест сканера с полными моками."""

    start_time = time.time()

    # Диагностические данные с правильной типизацией
    diagnostic_data: dict[str, Any] = {
        # 1. СЛУЖЕБНАЯ ИНФОРМАЦИЯ
        "test_info": {
            "version": "1.0",
            "test_type": "scanner_integration_test",
            "use_cache": use_cache,
            "stages": [],
            "errors": [],
            "timing": {},
            "temp_directory": str(temp_dir),
        },
        # 2. СТАТИСТИКА СКАНИРОВАНИЯ
        "scan_statistics": {
            "files_found": 0,
            "new_tasks": 0,
            "skipped_no_id": 0,
            "skipped_processed": 0,
            "skipped_queued": 0,
            "source_breakdown": {},
        },
        # 3. СОЗДАННЫЕ ЗАДАЧИ
        "created_tasks": [],
        # 4. ОПЕРАЦИИ REDIS
        "redis_operations": {
            "queue_operations": [],
            "set_operations": [],
            "cache_operations": [],
        },
    }

    try:
        # Создаем тестовую структуру источников
        test_sources = create_test_sources_structure(temp_dir)
        stages_list: list[str] = diagnostic_data["test_info"]["stages"]
        stages_list.append("✅ Создана тестовая структура источников")

        # Создаем мок Redis клиента
        mock_redis = create_mock_redis_client()
        setup_mock_queued_tasks(mock_redis, test_sources)
        stages_list.append("✅ Настроен мок Redis клиента")

        # Создаем мок подключения к БД
        mock_db_conn = create_mock_db_connection(test_sources)
        stages_list.append("✅ Настроен мок подключения БД")

        # Временно подменяем пути к источникам
        test_source_dirs = [info["source_dir"] for info in test_sources.values()]

        with (
            patch.multiple(
                "app.ingestion.scanner",
                get_redis_connection=lambda: mock_redis,
            ),
            patch("app.database.connection.get_db_connection") as mock_get_db,
            patch.object(settings, "SOURCE_DIRS", test_source_dirs),
        ):
            stages_list.append("✅ Применены моки и патчи")

            # Настраиваем мок БД
            mock_get_db.return_value.__enter__.return_value = mock_db_conn
            mock_get_db.return_value.__exit__.return_value = None

            # Запускаем сканирование
            scan_and_register_new_files(use_cache=use_cache)

            stages_list.append("✅ Выполнено сканирование источников")

        # Анализируем результаты из мок Redis состояния
        redis_state = mock_redis._test_state
        queues_dict = redis_state["queues"]
        new_tasks_list = queues_dict[settings.QUEUE_PARSING_NEW]
        assert isinstance(new_tasks_list, list)

        # Собираем статистику по созданным задачам
        diagnostic_data["scan_statistics"]["new_tasks"] = len(new_tasks_list)

        # Анализируем каждую созданную задачу
        created_tasks_list: list[dict[str, Any]] = diagnostic_data["created_tasks"]
        for task_json in new_tasks_list:
            try:
                task_data = json.loads(task_json)
                created_tasks_list.append(
                    {
                        "source_type": task_data["source_type"],
                        "source_id": task_data["source_id"],
                        "filename": task_data["filename"],
                    }
                )
            except json.JSONDecodeError:
                pass

        # Собираем подробную статистику по источникам
        total_files = 0
        source_breakdown: dict[str, dict[str, int]] = diagnostic_data["scan_statistics"]["source_breakdown"]
        for source_name, source_info in test_sources.items():
            file_counts = {
                "total": 0,
                "new": 0,
                "processed_in_db": 0,
                "queued": 0,
                "invalid_id": 0,
            }

            for file_info in source_info["files"]:
                file_counts["total"] += 1
                file_counts[file_info["category"]] += 1

            total_files += file_counts["total"]
            source_breakdown[source_name] = file_counts

        diagnostic_data["scan_statistics"]["files_found"] = total_files

        # Вычисляем ожидаемые пропуски на основе тестовых данных
        expected_processed = sum(counts["processed_in_db"] for counts in source_breakdown.values())
        expected_queued = sum(counts["queued"] for counts in source_breakdown.values())
        expected_invalid = sum(counts["invalid_id"] for counts in source_breakdown.values())

        diagnostic_data["scan_statistics"]["skipped_processed"] = expected_processed
        diagnostic_data["scan_statistics"]["skipped_queued"] = expected_queued
        diagnostic_data["scan_statistics"]["skipped_no_id"] = expected_invalid

        # Записываем операции Redis
        queue_ops_list: list[dict[str, Any]] = diagnostic_data["redis_operations"]["queue_operations"]
        queue_ops_list.append(
            {
                "operation": "LPUSH",
                "queue": settings.QUEUE_PARSING_NEW,
                "count": len(new_tasks_list),
            }
        )

        set_ops_list: list[dict[str, Any]] = diagnostic_data["redis_operations"]["set_operations"]
        set_ops_list.append(
            {
                "operation": "SADD",
                "set": settings.SET_QUEUED_IDS,
                "count": len(new_tasks_list),
            }
        )

        if use_cache:
            cache_ops_list: list[dict[str, str]] = diagnostic_data["redis_operations"]["cache_operations"]
            cache_ops_list.append({"operation": "sync_with_db", "status": "mocked"})

        stages_list.append("✅ Собрана диагностическая информация")

    except Exception as e:
        error_info = {
            "type": type(e).__name__,
            "message": str(e),
            "stage": "scanner_test",
        }
        error_stages: list[str] = diagnostic_data["test_info"]["stages"]
        error_list: list[dict[str, str]] = diagnostic_data["test_info"]["errors"]
        error_list.append(error_info)
        error_stages.append(f"❌ Ошибка: {type(e).__name__}")

    finally:
        end_time = time.time()
        timing_dict: dict[str, float] = diagnostic_data["test_info"]["timing"]
        timing_dict["total_seconds"] = round(end_time - start_time, 2)

    return diagnostic_data


def test_cache_operations(temp_dir: Path) -> dict[str, Any]:
    """Тестирует операции с кэшем: sync-only и clear-cache."""

    start_time = time.time()

    diagnostic_data: dict[str, Any] = {
        "test_info": {
            "version": "1.0",
            "test_type": "cache_operations_test",
            "stages": [],
            "errors": [],
            "timing": {},
        },
        "cache_operations": [],
    }

    try:
        # Создаем мок Redis
        mock_redis = create_mock_redis_client()

        with patch("app.ingestion.scanner.get_redis_connection", return_value=mock_redis):
            # Тест sync_redis_with_db
            with patch("app.ingestion.scanner.get_db_connection") as mock_get_db:
                mock_conn = MagicMock()
                mock_cursor = MagicMock()
                mock_cursor.fetchmany.return_value = []
                mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
                mock_get_db.return_value.__enter__.return_value = mock_conn

                sync_redis_with_db(mock_redis)
                cache_ops_list: list[dict[str, str]] = diagnostic_data["cache_operations"]
                cache_ops_list.append({"operation": "sync_redis_with_db", "status": "success"})
                stages_list: list[str] = diagnostic_data["test_info"]["stages"]
                stages_list.append("✅ Тест sync_redis_with_db")

            # Тест clear_redis_cache
            clear_redis_cache(mock_redis)
            cache_ops_list.append({"operation": "clear_redis_cache", "status": "success"})
            stages_list.append("✅ Тест clear_redis_cache")

    except Exception as e:
        error_info = {
            "type": type(e).__name__,
            "message": str(e),
            "stage": "cache_operations",
        }
        cache_error_stages: list[str] = diagnostic_data["test_info"]["stages"]
        cache_error_list: list[dict[str, str]] = diagnostic_data["test_info"]["errors"]
        cache_error_list.append(error_info)
        cache_error_stages.append(f"❌ Ошибка: {type(e).__name__}")

    finally:
        end_time = time.time()
        timing_dict: dict[str, float] = diagnostic_data["test_info"]["timing"]
        timing_dict["total_seconds"] = round(end_time - start_time, 2)

    return diagnostic_data


def save_diagnostic_report(diagnostic_data: dict[str, Any], output_file: Path) -> None:
    """Сохраняет диагностический отчет в JSON файл."""

    diagnostic_data["test_info"]["generated_at"] = time.time()

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(diagnostic_data, f, indent=2, ensure_ascii=False, default=str)


def main() -> int:
    """Основная функция тестирования."""

    parser = argparse.ArgumentParser(
        description="Тест сканера источников (run_10_scan_sources.py)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:

  Базовый тест без кэша:
    python tools/run_02_test_pipeline_01.py

  Тест с эмуляцией кэша:
    python tools/run_02_test_pipeline_01.py --use-cache

  Тест операций кэша:
    python tools/run_02_test_pipeline_01.py --test-cache-only

  Подробный вывод:
    python tools/run_02_test_pipeline_01.py --verbose
        """,
    )

    parser.add_argument(
        "--use-cache",
        action="store_true",
        help="Тестировать режим с предзагрузкой кэша",
    )

    parser.add_argument(
        "--test-cache-only",
        action="store_true",
        help="Тестировать только операции кэша (sync, clear)",
    )

    parser.add_argument(
        "--output-file",
        default="tools/result_diagnostic_pipeline01_scanner.json",
        help="Файл для сохранения диагностики",
    )

    parser.add_argument("--verbose", action="store_true", help="Подробный вывод")

    args = parser.parse_args()

    # Настройка логирования
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=log_level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")

    logger = logging.getLogger(__name__)

    try:
        logger.info("🚀 Запуск интеграционного теста сканера источников")

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            if args.test_cache_only:
                # Тестируем только операции кэша
                diagnostic_data = test_cache_operations(temp_path)
            else:
                # Полный тест сканера
                diagnostic_data = run_scanner_test(temp_path, use_cache=args.use_cache)

        # Сохраняем диагностический отчет
        output_path = Path(args.output_file)
        save_diagnostic_report(diagnostic_data, output_path)

        logger.info(f"📊 Диагностический отчет сохранен: {output_path}")

        # Выводим краткую сводку
        print("\n📋 КРАТКАЯ СВОДКА ТЕСТА СКАНЕРА:")
        stages_list: list[str] = diagnostic_data["test_info"]["stages"]
        errors_list: list[dict[str, str]] = diagnostic_data["test_info"]["errors"]
        print(f"Этапов выполнено: {len(stages_list)}")
        print(f"Ошибок: {len(errors_list)}")

        if not args.test_cache_only:
            stats = diagnostic_data.get("scan_statistics", {})
            print(f"Файлов найдено: {stats.get('files_found', 0)}")
            print(f"Новых задач: {stats.get('new_tasks', 0)}")
            print(f"Пропущено дубликатов: {stats.get('skipped_processed', 0) + stats.get('skipped_queued', 0)}")

        timing_dict: dict[str, Union[float, str]] = diagnostic_data["test_info"]["timing"]
        print(f"Время выполнения: {timing_dict.get('total_seconds', 'N/A')} сек")

        if errors_list:
            print("❌ ЕСТЬ ОШИБКИ!")
            return 1
        else:
            print("✅ ВСЕ ЭТАПЫ ПРОЙДЕНЫ УСПЕШНО!")
            print("\n🔍 ПРОВЕРКА МОКОВ:")
            print("✅ Redis операции - ЗАМОКАНЫ (нет реальных записей)")
            print("✅ PostgreSQL проверки - ЗАМОКАНЫ (предустановленные ответы)")
            print("✅ Файловая система - ИЗОЛИРОВАНА (временная директория)")
            return 0

    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
"""
Полный интеграционный тест скрипта восстановления run_00_recovery.py.

ТЕСТИРУЕТ ВЕСЬ ПРОЦЕСС ВОССТАНОВЛЕНИЯ:
- 0️⃣ Восстановление SET активных задач (rebuild_queued_set)
- 1️⃣ Восстановление зависших задач (TaskMonitor.check_and_recover_stale_tasks)
- 2️⃣ Поиск зависших блокировок и книг без артефактов (новая логика)
- 3️⃣ Очистка поврежденных записей Redis (_cleanup_redis)
- 4️⃣ Проверка целостности данных (check_data_integrity)

ИСПОЛЬЗУЕТ ВСЕ РЕАЛЬНЫЕ КОМПОНЕНТЫ:
- SystemRecovery с полной бизнес-логикой
- <PERSON><PERSON><PERSON><PERSON>, Task<PERSON>ueue<PERSON>ger, FileManager
- Все функции работы с файлами и путями
- Централизованные функции проверки (utils, database.queries)

МОКАЮТСЯ ТОЛЬКО ОПЕРАЦИИ I/O:
- Redis операции (через fakeredis)
- PostgreSQL запросы (через unittest.mock)
- Файловые операции (создаются временные директории)

СИМУЛИРУЕТ РЕАЛЬНЫЕ СЦЕНАРИИ СБОЕВ:
- Зависшие задачи в QUEUE_PROCESSING
- Потерянные файлы в in_progress директориях
- Поврежденные записи в Redis
- Несоответствие файлов и БД

РЕЗУЛЬТАТ: максимально честная диагностика процесса восстановления.
"""

import argparse
import json
import logging
import sys
import tempfile
import time
from pathlib import Path
from typing import Any
from unittest.mock import patch

# Добавление корневой директории проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Настройка fakeredis ДО импорта модулей проекта
import fakeredis


def setup_fake_redis():
    """Настраивает fakeredis для эмуляции Redis"""
    fake_redis = fakeredis.FakeRedis(decode_responses=False)
    return fake_redis


# Импорты после настройки fakeredis
from app import settings  # noqa: E402
from run_00_recovery import SystemRecovery  # noqa: E402


class RecoveryTestScenario:
    """Создает тестовый сценарий для восстановления системы."""

    def __init__(self, temp_dir: Path, fake_redis: fakeredis.FakeRedis):
        self.temp_dir = temp_dir
        self.fake_redis = fake_redis
        self.source_dirs: list[Path] = []
        self.scenarios: dict[str, list[Any]] = {
            "stale_tasks": [],
            "orphaned_files": [],
            "processing_files": [],
            "db_records": [],
        }

    def create_source_directories(self, source_types: list[int]) -> dict[int, Path]:
        """Создает временные директории источников."""
        source_dir_map = {}

        for source_type in source_types:
            # Создаем структуру директорий
            source_root = self.temp_dir / f"source_{source_type}"
            source_root.mkdir(exist_ok=True)

            # Поддиректории для обработки
            (source_root / "in_progress").mkdir(exist_ok=True)
            (source_root / "processed").mkdir(exist_ok=True)
            (source_root / "quarantine").mkdir(exist_ok=True)

            source_dir_map[source_type] = source_root
            self.source_dirs.append(source_root)

        return source_dir_map

    def add_stale_task(
        self,
        source_type: int,
        source_id: int,
        file_path: str,
        claimed_at: float,
        malformed: bool = False,
    ):
        """Добавляет зависшую задачу в QUEUE_PROCESSING."""
        task_data = {
            "source_type": source_type,
            "source_id": source_id,
            "filename": f"{source_id}.zip",
            "file_path": file_path,
            "_claimed_at": claimed_at,
        }

        if malformed:
            # Поврежденная задача
            raw_task = "INVALID_JSON_DATA"
        else:
            raw_task = json.dumps(task_data)

        self.fake_redis.lpush(settings.QUEUE_PARSING_PROCESSING, raw_task)
        self.scenarios["stale_tasks"].append({"task_data": task_data, "raw_task": raw_task, "malformed": malformed})

    def add_orphaned_file(self, source_dir: Path, source_id: int, in_db: bool = False):
        """Создает потерянный файл в in_progress."""
        in_progress_dir = source_dir / "in_progress"
        file_path = in_progress_dir / f"{source_id}.zip"

        # Создаем реальный файл
        file_path.write_text(f"fake content for {source_id}")

        self.scenarios["orphaned_files"].append({"file_path": file_path, "source_id": source_id, "in_db": in_db})

    def add_processing_file_to_redis(self, source_type: int, source_id: int, file_path: str):
        """Добавляет файл в список обрабатываемых в Redis."""
        task_data = {
            "source_type": source_type,
            "source_id": source_id,
            "file_path": file_path,
            "_claimed_at": time.time(),
        }

        raw_task = json.dumps(task_data)
        self.fake_redis.lpush(settings.QUEUE_PARSING_PROCESSING, raw_task)
        self.scenarios["processing_files"].append({"file_path": file_path, "task_data": task_data})

    def add_completed_task(self, source_type: int, source_id: int):
        """Добавляет завершенную задачу в QUEUE_COMPLETED."""
        task_data = {
            "source_type": source_type,
            "source_id": source_id,
            "filename": f"{source_id}.zip",
            "_completed_at": time.time(),
        }

        raw_task = json.dumps(task_data)
        self.fake_redis.lpush(settings.QUEUE_COMPLETED, raw_task)

    def add_new_task(self, source_type: int, source_id: int):
        """Добавляет новую задачу в QUEUE_PARSING_NEW."""
        task_data = {
            "source_type": source_type,
            "source_id": source_id,
            "filename": f"{source_id}.zip",
        }

        raw_task = json.dumps(task_data)
        self.fake_redis.lpush(settings.QUEUE_PARSING_NEW, raw_task)

    def populate_queued_ids_set(self, inconsistent: bool = False):
        """Заполняет SET_QUEUED_IDS."""
        # Добавляем ID из всех задач
        for scenario_list in [
            self.scenarios["stale_tasks"],
            self.scenarios["processing_files"],
        ]:
            for item in scenario_list:
                task_data = item.get("task_data", {})
                source_type = task_data.get("source_type")
                source_id = task_data.get("source_id")
                if source_type and source_id:
                    self.fake_redis.sadd(settings.SET_QUEUED_IDS, f"{source_type}:{source_id}")

        if inconsistent:
            # Добавляем лишние ID для симуляции несоответствий
            self.fake_redis.sadd(settings.SET_QUEUED_IDS, "999:888777")


def create_complex_test_scenario(temp_dir: Path, fake_redis: fakeredis.FakeRedis) -> RecoveryTestScenario:
    """Создает комплексный тестовый сценарий для восстановления."""
    scenario = RecoveryTestScenario(temp_dir, fake_redis)

    # Создаем директории источников
    source_dirs = scenario.create_source_directories([1, 2])

    current_time = time.time()
    old_time = current_time - settings.WORKER_TIMEOUT - 100  # Старше таймаута

    # 1. Зависшие задачи (старше WORKER_TIMEOUT)
    scenario.add_stale_task(
        source_type=1,
        source_id=123456,
        file_path=str(source_dirs[1] / "123456.zip"),
        claimed_at=old_time,
    )

    scenario.add_stale_task(
        source_type=2,
        source_id=789012,
        file_path=str(source_dirs[2] / "789012.zip"),
        claimed_at=old_time,
    )

    # 2. Поврежденная задача
    scenario.add_stale_task(
        source_type=1,
        source_id=999999,
        file_path="invalid_path",
        claimed_at=old_time,
        malformed=True,
    )

    # 3. Потерянные файлы в in_progress
    scenario.add_orphaned_file(source_dirs[1], 654321, in_db=True)  # В БД, нужно в processed
    scenario.add_orphaned_file(source_dirs[2], 987654, in_db=False)  # Не в БД, вернуть назад

    # 3a. Создаем in_progress файлы для зависших задач (чтобы были действительно восстановлены)
    in_progress_1 = source_dirs[1] / "in_progress" / "123456.zip"
    in_progress_1.write_text("fake content for stale task 123456")

    in_progress_2 = source_dirs[2] / "in_progress" / "789012.zip"
    in_progress_2.write_text("fake content for stale task 789012")

    # 4. Активные задачи в обработке (НЕ зависшие)
    scenario.add_processing_file_to_redis(source_type=1, source_id=111222, file_path=str(source_dirs[1] / "111222.zip"))

    # 5. Завершенные и новые задачи для rebuild_queued_set
    scenario.add_completed_task(source_type=1, source_id=555666)
    scenario.add_new_task(source_type=2, source_id=777888)

    # 6. Заполняем SET с несоответствиями
    scenario.populate_queued_ids_set(inconsistent=True)

    return scenario


def create_mock_database_functions():
    """Создает моки для функций базы данных."""

    def mock_is_source_processed(source_type: int, source_id: int) -> bool:
        """Мок функции проверки обработанности файла."""
        # Симулируем что файл 654321 уже обработан
        if source_id == 654321:
            return True
        return False

    def mock_check_data_integrity() -> dict[str, Any]:
        """Мок функции проверки целостности данных."""
        return {
            "total_books": 1250,
            "orphaned_sources": 3,
            "orphaned_authors": 12,
            "errors": 0,
        }

    def mock_extract_source_info_from_path(file_path: Path) -> dict[str, Any] | None:
        """Мок функции извлечения информации из пути."""
        # Извлекаем source_id из имени файла
        import re

        match = re.search(r"(\d+)\.zip$", str(file_path))
        if match:
            source_id = int(match.group(1))
            # Определяем source_type по родительской директории
            if "source_1" in str(file_path):
                source_type = 1
            elif "source_2" in str(file_path):
                source_type = 2
            else:
                source_type = 1  # По умолчанию

            return {"source_type": source_type, "source_id": source_id}
        return None

    def mock_get_source_dir_by_type(source_type: int) -> Path | None:
        """Мок функции получения директории по типу."""
        # В реальном тесте это будут временные директории
        # Используем безопасный путь для временных файлов
        import tempfile

        return Path(tempfile.gettempdir()) / "test_sources"  # nosec: B108

    return {
        "is_source_processed": mock_is_source_processed,
        "check_data_integrity": mock_check_data_integrity,
        "extract_source_info_from_path": mock_extract_source_info_from_path,
        "get_source_dir_by_type": mock_get_source_dir_by_type,
    }


def run_recovery_test_with_mocks(scenario: RecoveryTestScenario, source_dirs: dict[int, Path]) -> dict[str, Any]:
    """Запускает тест восстановления с правильными моками."""

    # Диагностические данные
    diagnostic_data: dict[str, Any] = {
        "test_info": {
            "version": "1.0",
            "test_type": "system_recovery_integration_test",
            "stages": [],
            "errors": [],
            "timing": {},
            "scenario_stats": {
                "stale_tasks_created": len(scenario.scenarios["stale_tasks"]),
                "orphaned_files_created": len(scenario.scenarios["orphaned_files"]),
                "processing_files_created": len(scenario.scenarios["processing_files"]),
            },
        },
        "recovery_results": {
            "set_rebuild": {},
            "stale_tasks": {},
            "orphaned_files": {},
            "redis_cleanup": {},
            "data_integrity": {},
        },
        "redis_state_before": {},
        "redis_state_after": {},
    }

    start_time = time.time()

    # Создаем моки для функций
    mock_functions = create_mock_database_functions()

    # Специальный мок для get_source_dir_by_type с учетом тестовых директорий
    def mock_get_source_dir_by_type_context(source_type: int) -> Path | None:
        return source_dirs.get(source_type)

    mock_functions["get_source_dir_by_type"] = mock_get_source_dir_by_type_context

    # Мок для settings.SOURCE_DIRS
    mock_source_dirs = list(source_dirs.values())

    # Мок для settings.get_processing_directories
    def mock_get_processing_directories(source_dir: Path) -> dict[str, Path]:
        return {
            "source": source_dir,
            "in_progress": source_dir / "in_progress",
            "processed": source_dir / "processed",
            "quarantine": source_dir / "quarantine",
        }

    try:
        # Получаем состояние очередей
        before = {
            "queue_new": scenario.fake_redis.llen(settings.QUEUE_PARSING_NEW),
            "queue_processing": scenario.fake_redis.llen(settings.QUEUE_PARSING_PROCESSING),
            "queue_completed": scenario.fake_redis.llen(settings.QUEUE_COMPLETED),
            # "set_processed": 0,  # SET_PROCESSED упразднен
            "set_queued_ids": scenario.fake_redis.scard(settings.SET_QUEUED_IDS),
        }

        diagnostic_data["redis_state_before"] = before
        diagnostic_data["test_info"]["stages"].append("📊 Начальное состояние Redis зафиксировано")

        # Патчим все необходимые зависимости
        with (
            patch("redis.from_url", return_value=scenario.fake_redis),
            patch(
                "app.ingestion.scanner.get_redis_connection",
                return_value=scenario.fake_redis,
            ),
            patch(
                "app.database.queries.is_source_processed",
                side_effect=mock_functions["is_source_processed"],
            ),
            patch(
                "app.database.queries.check_data_integrity",
                side_effect=mock_functions["check_data_integrity"],
            ),
            patch(
                "app.utils.extract_source_info_from_path",
                side_effect=mock_functions["extract_source_info_from_path"],
            ),
            patch(
                "app.utils.get_source_dir_by_type",
                side_effect=mock_functions["get_source_dir_by_type"],
            ),
            patch.object(settings, "SOURCE_DIRS", mock_source_dirs),
            patch.object(
                settings,
                "get_processing_directories",
                side_effect=mock_get_processing_directories,
            ),
        ):
            # Создаем и запускаем SystemRecovery
            recovery = SystemRecovery()
            diagnostic_data["test_info"]["stages"].append("🏗️ SystemRecovery инициализирован")

            # Патчим методы SystemRecovery для сбора диагностики
            def patched_run_full_recovery():
                try:
                    diagnostic_data["test_info"]["stages"].append("🚨 Начинается аварийное восстановление системы")

                    # 0. Восстановление SET активных задач
                    diagnostic_data["test_info"]["stages"].append("0️⃣ Восстановление SET активных задач...")
                    from app.processing.queue_manager import rebuild_queued_set

                    rebuild_queued_set(scenario.fake_redis)

                    # Проверяем результат rebuild
                    final_set_size = scenario.fake_redis.scard(settings.SET_QUEUED_IDS)
                    diagnostic_data["recovery_results"]["set_rebuild"] = {
                        "success": True,
                        "final_set_size": final_set_size,
                    }
                    diagnostic_data["test_info"]["stages"].append(f"✅ SET восстановлен, размер: {final_set_size}")

                    # 1. Восстановление зависших задач
                    diagnostic_data["test_info"]["stages"].append("1️⃣ Восстановление зависших задач...")
                    stale_stats = recovery.task_monitor.check_and_recover_stale_tasks()
                    diagnostic_data["recovery_results"]["stale_tasks"] = stale_stats
                    diagnostic_data["test_info"]["stages"].append(
                        f"✅ Зависшие задачи: восстановлено={stale_stats.get('recovered', 0)}, "
                        f"в карантин={stale_stats.get('quarantined', 0)}, ошибок={stale_stats.get('errors', 0)}"
                    )

                    # 2. Сверка файлов с БД
                    diagnostic_data["test_info"]["stages"].append("2️⃣ Сверка файлов в /in_progress/ с БД...")
                    orphan_stats = recovery._recover_orphaned_files()
                    diagnostic_data["recovery_results"]["orphaned_files"] = orphan_stats
                    diagnostic_data["test_info"]["stages"].append(
                        f"✅ Потерянные файлы: найдено={orphan_stats.get('found_orphans', 0)}, "
                        f"восстановлено={orphan_stats.get('recovered', 0)}, ошибок={orphan_stats.get('errors', 0)}"
                    )

                    # 3. Очистка поврежденных записей Redis
                    diagnostic_data["test_info"]["stages"].append("3️⃣ Очистка поврежденных записей Redis...")
                    redis_stats = recovery._cleanup_redis()
                    diagnostic_data["recovery_results"]["redis_cleanup"] = redis_stats
                    diagnostic_data["test_info"]["stages"].append(
                        f"✅ Redis очистка: поврежденных={redis_stats.get('malformed_tasks', 0)}, "
                        f"очищено={redis_stats.get('cleaned', 0)}, ошибок={redis_stats.get('errors', 0)}"
                    )

                    # 4. Проверка целостности данных
                    diagnostic_data["test_info"]["stages"].append("4️⃣ Проверка целостности данных...")
                    integrity_stats = mock_functions["check_data_integrity"]()
                    diagnostic_data["recovery_results"]["data_integrity"] = integrity_stats
                    diagnostic_data["test_info"]["stages"].append(
                        f"✅ Целостность данных: книг={integrity_stats.get('total_books', 0)}, "
                        f"потерянных источников={integrity_stats.get('orphaned_sources', 0)}"
                    )

                    diagnostic_data["test_info"]["stages"].append("✅ Восстановление завершено успешно")

                except Exception as e:
                    error_info = {
                        "type": type(e).__name__,
                        "message": str(e),
                        "stage": "recovery_process",
                    }
                    diagnostic_data["test_info"]["errors"].append(error_info)
                    diagnostic_data["test_info"]["stages"].append(f"❌ Ошибка восстановления: {type(e).__name__}")
                    raise

            # Запускаем восстановление
            patched_run_full_recovery()

    except Exception as e:
        error_info = {
            "type": type(e).__name__,
            "message": str(e),
            "stage": "test_execution",
        }
        diagnostic_data["test_info"]["errors"].append(error_info)
        diagnostic_data["test_info"]["stages"].append(f"❌ Критическая ошибка теста: {type(e).__name__}")

    finally:
        # Получаем состояние после
        after = {
            "queue_new": scenario.fake_redis.llen(settings.QUEUE_PARSING_NEW),
            "queue_processing": scenario.fake_redis.llen(settings.QUEUE_PARSING_PROCESSING),
            "queue_completed": scenario.fake_redis.llen(settings.QUEUE_COMPLETED),
            "set_processed": scenario.fake_redis.scard(settings.SET_PROCESSED),
            "set_queued_ids": scenario.fake_redis.scard(settings.SET_QUEUED_IDS),
        }

        diagnostic_data["redis_state_after"] = after
        end_time = time.time()
        diagnostic_data["test_info"]["timing"]["total_seconds"] = round(end_time - start_time, 2)

    return diagnostic_data


def save_diagnostic_report(diagnostic_data: dict[str, Any], output_file: Path):
    """Сохраняет диагностический отчет в JSON файл."""
    diagnostic_data["test_info"]["generated_at"] = time.time()

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(diagnostic_data, f, indent=2, ensure_ascii=False, default=str)


def main():
    """Основная функция тестирования."""

    parser = argparse.ArgumentParser(description="Тест системы восстановления run_00_recovery.py")
    parser.add_argument(
        "--output-file",
        default="tools/result_diagnostic_pipeline00_recovery_output.json",
        help="Файл для сохранения диагностики",
    )
    parser.add_argument("--verbose", action="store_true", help="Подробный вывод")

    args = parser.parse_args()

    # Настройка логирования
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=log_level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")

    logger = logging.getLogger(__name__)

    try:
        logger.info("🚀 Запуск теста системы восстановления")

        # Настройка fakeredis
        fake_redis = setup_fake_redis()

        # Создание временных директорий
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            logger.info(f"📁 Временная директория: {temp_path}")

            # Создание тестового сценария
            scenario = create_complex_test_scenario(temp_path, fake_redis)
            source_dirs = {1: temp_path / "source_1", 2: temp_path / "source_2"}

            logger.info("📋 Тестовый сценарий создан:")
            logger.info(f"   - Зависших задач: {len(scenario.scenarios['stale_tasks'])}")
            logger.info(f"   - Потерянных файлов: {len(scenario.scenarios['orphaned_files'])}")
            logger.info(f"   - Файлов в обработке: {len(scenario.scenarios['processing_files'])}")

            # Запуск теста
            diagnostic_data = run_recovery_test_with_mocks(scenario, source_dirs)

            # Сохранение отчета
            output_path = Path(args.output_file)
            save_diagnostic_report(diagnostic_data, output_path)

            logger.info(f"📊 Диагностический отчет сохранен: {output_path}")

            # Вывод краткой сводки
            print("\n📋 КРАТКАЯ СВОДКА ТЕСТА ВОССТАНОВЛЕНИЯ:")
            print(f"Этапов выполнено: {len(diagnostic_data['test_info']['stages'])}")
            print(f"Ошибок: {len(diagnostic_data['test_info']['errors'])}")
            print(f"Время выполнения: {diagnostic_data['test_info']['timing'].get('total_seconds', 'N/A')} сек")

            # Детальная сводка по этапам восстановления
            print("\n🔧 РЕЗУЛЬТАТЫ ВОССТАНОВЛЕНИЯ:")

            recovery_results = diagnostic_data.get("recovery_results", {})

            # SET rebuild
            set_rebuild = recovery_results.get("set_rebuild", {})
            print(
                f"0️⃣ SET активных задач: {'✅' if set_rebuild.get('success') else '❌'} "
                f"(размер: {set_rebuild.get('final_set_size', 'N/A')})"
            )

            # Зависшие задачи
            stale_tasks = recovery_results.get("stale_tasks", {})
            print(
                f"1️⃣ Зависшие задачи: восстановлено={stale_tasks.get('recovered', 0)}, "
                f"в карантин={stale_tasks.get('quarantined', 0)}, ошибок={stale_tasks.get('errors', 0)}"
            )

            # Потерянные файлы
            orphaned = recovery_results.get("orphaned_files", {})
            print(
                f"2️⃣ Потерянные файлы: найдено={orphaned.get('found_orphans', 0)}, "
                f"восстановлено={orphaned.get('recovered', 0)}, ошибок={orphaned.get('errors', 0)}"
            )

            # Redis очистка
            redis_cleanup = recovery_results.get("redis_cleanup", {})
            print(
                f"3️⃣ Redis очистка: поврежденных={redis_cleanup.get('malformed_tasks', 0)}, "
                f"очищено={redis_cleanup.get('cleaned', 0)}, ошибок={redis_cleanup.get('errors', 0)}"
            )

            # Целостность данных
            integrity = recovery_results.get("data_integrity", {})
            print(
                f"4️⃣ Целостность данных: книг={integrity.get('total_books', 0)}, "
                f"потерянных источников={integrity.get('orphaned_sources', 0)}"
            )

            # Состояние Redis до/после
            print("\n📊 СОСТОЯНИЕ REDIS:")
            before = diagnostic_data.get("redis_state_before", {})
            after = diagnostic_data.get("redis_state_after", {})

            print("   ДО  | ПОСЛЕ")
            print(f"Новые:        {before.get('queue_new', 0):3d} | {after.get('queue_new', 0):3d}")
            print(f"Обработка:    {before.get('queue_processing', 0):3d} | {after.get('queue_processing', 0):3d}")
            print(f"Завершенные:  {before.get('queue_completed', 0):3d} | {after.get('queue_completed', 0):3d}")
            print(f"SET активных: {before.get('set_queued_ids', 0):3d} | {after.get('set_queued_ids', 0):3d}")

            if diagnostic_data["test_info"]["errors"]:
                print("\n❌ ЕСТЬ ОШИБКИ В ТЕСТЕ!")
                for error in diagnostic_data["test_info"]["errors"]:
                    print(f"   {error['type']}: {error['message']}")
                return 1
            else:
                print("\n✅ ВСЕ ЭТАПЫ ВОССТАНОВЛЕНИЯ ПРОЙДЕНЫ УСПЕШНО!")
                print("\n🔍 ПРОВЕРКА МОКОВ:")
                print("✅ Redis операции - ЗАМОКАНЫ (fakeredis)")
                print("✅ PostgreSQL запросы - ЗАМОКАНЫ (unittest.mock)")
                print("✅ Файловые операции - ВРЕМЕННЫЕ ДИРЕКТОРИИ")
                print("✅ Все компоненты системы - РЕАЛЬНЫЕ")
                return 0

    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

# run_00_recovery.py

import logging
import sys
from pathlib import Path

from app import settings
from app.database.queries import check_data_integrity, is_source_processed
from app.processing.queue_manager import TaskQueueManager
from app.processing.task_monitor import TaskMonitor
from app.processing.quarantine_processor import QuarantineProcessor


class SystemRecovery:
    """Скрипт восстановления для критических сбоев системы.
    Использует существующие компоненты вместо дублирования логики.
    """

    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)

        # Используем существующие компоненты вместо дублирования кода
        self.task_monitor = TaskMonitor()
        self.queue_manager = TaskQueueManager()
        self.quarantine_processor = QuarantineProcessor(enable_processing=settings.QUARANTINE_PROCESSING_ENABLED)

    def setup_logging(self):
        """Настройка логирования для восстановления"""
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler("recovery.log", encoding="utf-8"),
            ],
        )

    def run_full_recovery(self):
        """Запускает полное восстановление системы"""
        self.logger.info("🚨 НАЧИНАЕТСЯ АВАРИЙНОЕ ВОССТАНОВЛЕНИЕ СИСТЕМЫ")

        try:
            # 0. КРИТИЧНО: Восстанавливаем SET_QUEUED_IDS для предотвращения дубликатов
            self.logger.info("0️⃣ Восстановление SET активных задач...")
            from app.processing.queue_manager import get_redis_connection, rebuild_queued_set

            redis_client = get_redis_connection()
            rebuild_queued_set(redis_client)

            # 1. Восстанавливаем зависшие задачи (используем TaskMonitor)
            self.logger.info("1️⃣ Восстановление зависших задач...")
            stale_stats = self.task_monitor.check_and_recover_stale_tasks()

            # 2. Поиск зависших блокировок и книг без артефактов (новая логика)
            self.logger.info("2️⃣ Поиск зависших блокировок и книг без артефактов...")
            recovery_stats = self._recover_stale_locks_and_incomplete_books()

            # 3. Очищаем поврежденные записи Redis (используем TaskQueueManager)
            self.logger.info("3️⃣ Очистка поврежденных записей Redis...")
            redis_stats = self._cleanup_redis()

            # 4. Проверяем целостность данных (используем centralized queries)
            self.logger.info("4️⃣ Проверка целостности данных...")
            integrity_stats = check_data_integrity()

            # 5. Выводим итоговый отчет
            self._print_recovery_report(stale_stats, recovery_stats, redis_stats, integrity_stats)

        except Exception as e:
            self.logger.critical(f"💥 КРИТИЧЕСКАЯ ОШИБКА ПРИ ВОССТАНОВЛЕНИИ: {e}", exc_info=True)
            sys.exit(1)

    def _recover_stale_locks_and_incomplete_books(self) -> dict:
        """Новая логика восстановления для архитектуры без перемещения файлов.

        Ищет:
        1. Зависшие блокировки в Redis (ключи lock:book:* без TTL или с истекшим TTL)
        2. Книги со статусом process_status = 10 (метаданные сохранены) без артефакта
        """
        stats = {"stale_locks": 0, "incomplete_books": 0, "recovered": 0, "errors": 0}

        try:
            import redis
            redis_client = redis.from_url(settings.REDIS_URL)

            # 1. Поиск зависших блокировок
            self.logger.info("🔍 Поиск зависших блокировок Redis...")
            lock_pattern = "lock:book:*"
            stale_locks = []

            for key in redis_client.scan_iter(match=lock_pattern):
                try:
                    ttl = redis_client.ttl(key)
                    # TTL = -1 означает, что ключ существует, но без TTL (зависшая блокировка)
                    # TTL = -2 означает, что ключ не существует
                    if ttl == -1:
                        stale_locks.append(key.decode('utf-8'))
                        stats["stale_locks"] += 1
                except Exception as e:
                    self.logger.warning(f"Ошибка проверки TTL для {key}: {e}")

            if stale_locks:
                self.logger.warning(f"🔒 Найдено зависших блокировок: {len(stale_locks)}")
                for lock_key in stale_locks:
                    try:
                        redis_client.delete(lock_key)
                        self.logger.info(f"🗑️ Удалена зависшая блокировка: {lock_key}")
                        stats["recovered"] += 1
                    except Exception as e:
                        self.logger.error(f"Ошибка удаления блокировки {lock_key}: {e}")
                        stats["errors"] += 1
            else:
                self.logger.info("✅ Зависших блокировок не найдено")

            # 2. Поиск книг без артефактов
            self.logger.info("📚 Поиск книг со статусом 10 без артефактов...")
            incomplete_books = self._find_incomplete_books()
            stats["incomplete_books"] = len(incomplete_books)

            if incomplete_books:
                self.logger.warning(f"📋 Найдено книг без артефактов: {len(incomplete_books)}")
                for book_id, title in incomplete_books:
                    self.logger.info(f"📖 Книга без артефакта: {book_id} - {title[:50]}...")
            else:
                self.logger.info("✅ Все книги со статусом 10 имеют артефакты")

            return stats

        except Exception as e:
            self.logger.error(f"❌ Ошибка при восстановлении: {e}")
            stats["errors"] += 1
            return stats

    def _find_incomplete_books(self) -> list[tuple[str, str]]:
        """Находит книги с промежуточным статусом без артефактов."""
        incomplete_books = []

        try:
            from app.database.connection import get_db_connection

            with get_db_connection() as conn:
                with conn.cursor() as cur:
                    # Ищем книги со статусом 10 (метаданные сохранены)
                    cur.execute(
                        """
                        SELECT id, title
                        FROM books
                        WHERE process_status = 10
                        ORDER BY updated_at DESC
                        LIMIT 100
                    """
                    )

                    results = cur.fetchall()

                    for row in results:
                        book_id = str(row[0])
                        title = str(row[1])

                        # Проверяем существование артефакта
                        artifact_path = self._get_artifact_path(book_id)
                        if not artifact_path.exists():
                            incomplete_books.append((book_id, title))

        except Exception as e:
            self.logger.error(f"Ошибка поиска неполных книг: {e}")

        return incomplete_books

    def _get_artifact_path(self, book_id: str) -> Path:
        """Возвращает путь к артефакту книги."""
        from app import settings

        # Группировка по тысячам: 123456 → 123000
        book_id_int = int(book_id)
        thousand_folder = f"{book_id_int // 1000 * 1000:06d}"

        return settings.CANONICAL_STORAGE_PATH / thousand_folder / f"{book_id}.json"


    def _cleanup_redis(self) -> dict:
        """Очищает поврежденные записи в Redis.
        Использует TaskQueueManager для работы с очередями.
        """
        stats = {"malformed_tasks": 0, "cleaned": 0, "errors": 0}

        try:
            # Используем TaskQueueManager для поиска поврежденных задач
            stale_tasks = self.queue_manager.find_stale_tasks()

            malformed_count = 0
            for task in stale_tasks:
                if task.get("_malformed"):
                    malformed_count += 1

            stats["malformed_tasks"] = malformed_count

            if malformed_count > 0:
                self.logger.warning(f"🧹 Найдено {malformed_count} поврежденных задач")
                # TaskMonitor уже обрабатывает поврежденные задачи в check_and_recover_stale_tasks
                stats["cleaned"] = malformed_count

        except Exception as e:
            self.logger.error(f"❌ Ошибка очистки Redis: {e}")
            stats["errors"] += 1

        return stats





    def _print_recovery_report(
        self,
        stale_stats: dict,
        recovery_stats: dict,
        redis_stats: dict,
        integrity_stats: dict,
    ):
        """Выводит итоговый отчет о восстановлении"""
        self.logger.info("=" * 60)
        self.logger.info("📊 ИТОГОВЫЙ ОТЧЕТ О ВОССТАНОВЛЕНИИ СИСТЕМЫ")
        self.logger.info("=" * 60)

        # Статистика восстановления SET активных задач
        self.logger.info("🔄 SET активных задач: восстановлен из всех очередей")

        # Статистика зависших задач
        self.logger.info("🔧 Зависшие задачи:")
        self.logger.info(f"   - Восстановлено: {stale_stats.get('recovered', 0)}")
        self.logger.info(f"   - В карантин: {stale_stats.get('quarantined', 0)}")
        self.logger.info(f"   - Ошибок: {stale_stats.get('errors', 0)}")

        # Статистика блокировок и неполных книг
        self.logger.info("🔒 Блокировки и неполные книги:")
        self.logger.info(f"   - Зависших блокировок: {recovery_stats.get('stale_locks', 0)}")
        self.logger.info(f"   - Книг без артефактов: {recovery_stats.get('incomplete_books', 0)}")
        self.logger.info(f"   - Восстановлено: {recovery_stats.get('recovered', 0)}")
        self.logger.info(f"   - Ошибок: {recovery_stats.get('errors', 0)}")

        # Статистика Redis
        self.logger.info("🧹 Redis:")
        self.logger.info(f"   - Поврежденных задач: {redis_stats.get('malformed_tasks', 0)}")
        self.logger.info(f"   - Очищено: {redis_stats.get('cleaned', 0)}")
        self.logger.info(f"   - Ошибок: {redis_stats.get('errors', 0)}")

        # Статистика целостности данных
        self.logger.info("📖 Целостность данных:")
        self.logger.info(f"   - Всего книг: {integrity_stats.get('total_books', 0)}")
        self.logger.info(f"   - Потерянных источников: {integrity_stats.get('orphaned_sources', 0)}")
        self.logger.info(f"   - Потерянных авторов: {integrity_stats.get('orphaned_authors', 0)}")
        self.logger.info(f"   - Ошибок проверки: {integrity_stats.get('errors', 0)}")

        # Итоговый статус
        total_errors = (
            stale_stats.get("errors", 0)
            + orphan_stats.get("errors", 0)
            + redis_stats.get("errors", 0)
            + integrity_stats.get("errors", 0)
        )

        if total_errors == 0:
            self.logger.info("✅ ВОССТАНОВЛЕНИЕ ЗАВЕРШЕНО УСПЕШНО")
        else:
            self.logger.warning(f"⚠️ ВОССТАНОВЛЕНИЕ ЗАВЕРШЕНО С {total_errors} ОШИБКАМИ")

        self.logger.info("=" * 60)

        # Вывод статистики файлов
        file_stats = self.task_monitor.get_processing_files_stats()
        self.logger.info("📁 Состояние файловой системы после восстановления:")
        for detail in file_stats.get("details", []):
            source_name = detail.get("source_name", "unknown")
            self.logger.info(f"   {source_name}:")
            self.logger.info(f"      - В обработке: {detail.get('in_progress', 0)}")
            self.logger.info(f"      - Обработано: {detail.get('processed', 0)}")
            self.logger.info(f"      - В карантине: {detail.get('quarantine', 0)}")


def main():
    """Запуск скрипта восстановления"""
    recovery = SystemRecovery()
    recovery.run_full_recovery()


if __name__ == "__main__":
    main()
